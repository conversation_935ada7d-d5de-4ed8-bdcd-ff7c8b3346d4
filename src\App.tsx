import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect, useState } from "react";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import AnalysisHistory from "./pages/AnalysisHistory";
import Components from "./pages/Components";
import OneOnOneAnalysis from "./pages/OneOnOneAnalysis";
import RelationshipTypePage from "./pages/RelationshipTypePage";
import UserIdentificationPage from "./pages/UserIdentificationPage";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfService from "./pages/TermsOfService";
import Support from "./pages/Support";
import ChatExportGuide from "./components/ChatExportGuide";
import DashboardWalkthrough from "./components/DashboardWalkthrough";
import IntroTutorialPage from "./pages/IntroTutorialPage";
import AppIntroductionPage from "./pages/AppIntroductionPage";
import { AppStateProvider, useAppState } from "./contexts/AppStateContext";
import { AnalysisProvider } from "./contexts/AnalysisContext";
import { SafeAreaProvider } from "./contexts/SafeAreaContext";
import FileHandler from "./components/FileHandler";
import { lockToPortrait } from "./utils/screenOrientation";
import SubscriptionPage from "./pages/SubscriptionPage";
import { initializePurchases } from "./services/purchases";
import { initializeDevice } from "./services/device";
import { initializeRevenueCat } from "./services/revenuecat"; // Added RevenueCat
import RizzStyleSubscriptionModal from "./components/RizzStyleSubscriptionModal";
import { DailyLimitReachedDialog } from "./components/DailyLimitReachedDialog";
import { useVersionCheck } from "./hooks/useVersionCheck";
import ForceUpdateDialog from "./components/ForceUpdateDialog";
import VersionManager from "./components/VersionManager";
import CustomSplashScreen from "./components/CustomSplashScreen";
import AppIntroduction from "./components/AppIntroduction";
import { SplashScreen } from '@capacitor/splash-screen';
import { globalImagePreloader, CRITICAL_IMAGES, MAIN_PAGE_IMAGES } from './utils/imagePreloader';

const queryClient = new QueryClient();

const AppContent = () => {
  const {
    isSubscriptionModalOpen,
    hideSubscriptionModal,
    hideSubscriptionModalAfterSuccess,
    cancelSubscriptionFlow,
    postPurchaseAction,
    isDailyLimitDialogOpen,
    hideDailyLimitDialog,
    showSubscriptionModal
  } = useAppState();
  const [showVersionManager, setShowVersionManager] = useState(false);
  
  // Version checking with force update handling
  const { versionCheck, requiresUpdate } = useVersionCheck({
    checkOnMount: true,
    autoRefreshInterval: 5 * 60 * 1000, // Check every 5 minutes
    onUpdateRequired: (result) => {
      console.log('Update required:', result);
    }
  });

  // Show version manager in development mode with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Ctrl+Shift+V to open version manager (development only)
      if (event.ctrlKey && event.shiftKey && event.key === 'V') {
        if (process.env.NODE_ENV === 'development') {
          setShowVersionManager(true);
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  return (
    <>
      <FileHandler />
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/history" element={<AnalysisHistory />} />
        <Route path="/components" element={<Components />} />
        <Route path="/relationship-type" element={<RelationshipTypePage />} />
        <Route path="/user-identification" element={<UserIdentificationPage />} />
        <Route path="/one-on-one" element={<OneOnOneAnalysis />} />
        <Route path="/subscribe" element={<SubscriptionPage />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/terms-of-service" element={<TermsOfService />} />
        <Route path="/support" element={<Support />} />
        <Route path="/tutorial" element={<ChatExportGuide />} />
        <Route path="/intro-tutorial" element={<IntroTutorialPage />} />
        <Route path="/app-introduction" element={<AppIntroductionPage />} />
        <Route path="/dashboard-tutorial" element={<DashboardWalkthrough />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
      
      {/* Subscription Modal */}
      {isSubscriptionModalOpen && (
        <RizzStyleSubscriptionModal
          onClose={hideSubscriptionModal}
          onCancel={cancelSubscriptionFlow}
          onSuccess={() => {
            if (postPurchaseAction) {
              postPurchaseAction();
            }
            hideSubscriptionModalAfterSuccess();
          }}
        />
      )}
      
      {/* Daily Limit Dialog */}
      <DailyLimitReachedDialog
        open={isDailyLimitDialogOpen}
        onClose={hideDailyLimitDialog}
      />
      
      {/* Force Update Dialog */}
      {versionCheck && requiresUpdate && (
        <ForceUpdateDialog
          versionCheck={versionCheck}
          allowDismiss={false} // Force update is mandatory
          onUpdate={() => {
            console.log('User initiated update');
          }}
        />
      )}
      
      {/* Version Manager (Development Only) */}
      {process.env.NODE_ENV === 'development' && (
        <VersionManager
          isOpen={showVersionManager}
          onClose={() => setShowVersionManager(false)}
        />
      )}
    </>
  );
};

const App = () => {
  const [isInitializing, setIsInitializing] = useState(true);
  const [showAppIntro, setShowAppIntro] = useState(false);
  
  // Initialize RevenueCat and subscription system on app start
  useEffect(() => {
    const initApp = async () => {
      try {
        // Start image preloading immediately - combine critical and main page images
        const allMainImages = [...new Set([...CRITICAL_IMAGES, ...MAIN_PAGE_IMAGES])];
        const imagePreloadPromise = globalImagePreloader.preloadImages(allMainImages);
        
        // Initialize RevenueCat first with null to get anonymous ID
        await initializeRevenueCat(null);
        
        // Initialize device service (which now gets RevenueCat anonymous ID)
        const appUserId = await initializeDevice();

        if (!appUserId) {
          console.error("Failed to get a valid App User ID from RevenueCat.");
          return;
        }
        
        // Initialize purchases (which will now use RevenueCat)
        await initializePurchases();
        
        // Wait for both app services and critical images to be ready
        await Promise.all([
          imagePreloadPromise,
          new Promise(resolve => setTimeout(resolve, 500)) // Minimum splash time
        ]);
        
        console.log("App services and images initialized successfully");
        
        // Hide splash screen after initialization is complete
        await SplashScreen.hide();
        
        // Add another small delay before hiding custom splash
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Check if this is the first time user is opening the app
        const isFirstTime = !localStorage.getItem('app_intro_completed');
        setShowAppIntro(isFirstTime);
        setIsInitializing(false);
      } catch (error) {
        console.error("Error initializing app services:", error);
        // Hide splash screen even if initialization fails
        await SplashScreen.hide();
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // Check if this is the first time user is opening the app (even on error)
        const isFirstTime = !localStorage.getItem('app_intro_completed');
        setShowAppIntro(isFirstTime);
        setIsInitializing(false);
      }
    };
    
    initApp();
  }, []);

  // Lock screen orientation to portrait when app starts
  useEffect(() => {
    // Only run on mobile devices
    const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );

    if (isMobileDevice) {
      lockToPortrait();
    }
  }, []);

  if (isInitializing) {
    return <CustomSplashScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <SafeAreaProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AppStateProvider>
              <AnalysisProvider>
                {/* Show app introduction for first-time users */}
                {showAppIntro ? (
                  <AppIntroduction 
                    onComplete={() => setShowAppIntro(false)} 
                  />
                ) : (
                  <AppContent />
                )}
              </AnalysisProvider>
            </AppStateProvider>
          </BrowserRouter>
        </TooltipProvider>
      </SafeAreaProvider>
    </QueryClientProvider>
  );
};

export default App;
