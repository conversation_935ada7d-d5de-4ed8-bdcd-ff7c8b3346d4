import React, { useEffect } from 'react';

const CustomSplashScreen: React.FC = () => {
  // Preload critical images during splash screen
  useEffect(() => {
    const imagesToPreload = [
      '/icons/header_logo.webp',
      '/mainscreen.webp',
      '/icons/icon-192.webp',
      '/icons/icon-512.webp',
      // Add other critical images here
    ];

    imagesToPreload.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, []);
  return (
    <div className="fixed inset-0 z-50" style={{ backgroundColor: '#f0bbcc' }}>
      {/* Logo perfectly centered - matching AppIntroduction positioning */}
      <div
        className="absolute z-20"
        style={{
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        }}
      >
        <div className="relative flex flex-col items-center">
          <img
            src="/icons/header_logo.webp"
            alt="Chatbuster Logo"
            className="w-48 h-24 object-contain mb-4"
          />

          {/* Placeholder for visual consistency with AppIntroduction */}
          <div className="relative w-16 h-16 opacity-0" />
        </div>
      </div>
    </div>
  );
};

export default CustomSplashScreen;