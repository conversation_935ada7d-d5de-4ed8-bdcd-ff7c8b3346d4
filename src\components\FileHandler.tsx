import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/utils/toast';
import { AnalysisState, useAppState } from '@/contexts/AppStateContext';
import { useAnalysis } from '@/contexts/AnalysisContext';
import { analyzeChatText, analyzeChatFile } from '@/services/api';
import { isPremiumUser } from '@/services/purchases';
import { canAnalyze, getQuotaStatus } from '@/services/quota';
import JSZip from 'jszip';
import { App } from '@capacitor/app';
import { Filesystem, Directory } from '@capacitor/filesystem';
import { FileOpener } from '@capawesome-team/capacitor-file-opener';
import { Capacitor } from '@capacitor/core';
import { readFileWithProperEncoding, readUrlWithProperEncoding, decodeBase64WithProperEncoding } from '@/utils/fileUtils';
import { isGroupChat } from '@/utils/chatTypeDetector';
import { demoData } from '@/data/analysisData';
import { hapticFeedback } from '@/utils/haptics';
import { saveAnalysis } from '@/utils/analysisStorage';
import { generateChatHash, storeChatHash, extractFirstTenLines } from '@/utils/chatHashUtils';
import { isDevelopmentMode } from '@/utils/environment';
import { extractMessageCount, extractParticipantCount } from '@/utils/metadataExtraction';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { DailyLimitReachedDialog } from './DailyLimitReachedDialog';

// Track processed shared file URLs to prevent re-processing
const processedSharedFileUrls = new Set<string>();

/**
 * Initialize file handling for the app
 * @param onFileProcessed Callback function to be called when a file is processed
 */
export const initFileHandling = (
  onFileProcessed: (text: string, fileName: string) => void,
  isProcessingSharedFileRef: React.MutableRefObject<boolean>,
  isLoadingFromHistory: boolean
) => {
  // Create a hidden file input element
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = '.txt,.zip,.json';
  fileInput.style.display = 'none';
  fileInput.multiple = false;
  document.body.appendChild(fileInput);

  // Listen for file input changes
  fileInput.addEventListener('change', async () => {
    const files = fileInput.files;
    if (!files || files.length === 0) {
      // Reset the file input even if no files were selected
      fileInput.value = '';
      return;
    }

    const file = files[0];
    console.log('File selected via input:', file.name);

    try {
      await processFile(file, onFileProcessed);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      // Always reset the file input to allow selecting the same file again
      fileInput.value = '';
    }
  });

  // Listen for file share events
  document.addEventListener('paste', async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];

      // Check if the item is a file
      if (item.kind === 'file') {
        const file = item.getAsFile();
        if (!file) continue;

        console.log('File pasted:', file.name);
        await processFile(file, onFileProcessed);
        break;
      }
    }
  });

  // Listen for drag and drop events
  document.addEventListener('drop', async (event) => {
    event.preventDefault();

    const files = event.dataTransfer?.files;
    if (!files || files.length === 0) return;

    console.log('File dropped:', files[0].name);
    await processFile(files[0], onFileProcessed);
  });

  document.addEventListener('dragover', (event) => {
    event.preventDefault();
  });

  // Expose a function to trigger the file input
  (window as any).openFileInput = () => {
    // Reset any processing flags before opening file dialog
    if (isProcessingSharedFileRef.current) {
      console.log('Resetting processing flag before opening file dialog');
      isProcessingSharedFileRef.current = false;
    }
    fileInput.click();
  };

  // Listen for deep links (used for file handling in mobile apps)
  try {
    // Check if we're in a Capacitor environment
    if (typeof (window as any).Capacitor !== 'undefined') {
      console.log('🔧 Setting up appUrlOpen listener for platform:', Capacitor.getPlatform());
      
      App.addListener('appUrlOpen', async (event) => {
        
        // Special handling for Android content:// URIs
        if (Capacitor.getPlatform() === 'android' && event.url && event.url.startsWith('content://')) {
          console.log('🤖 Android content:// URI detected:', event.url);
        }

        // Check if we're loading from history - if so, skip processing
        if (isLoadingFromHistory) {
          console.log('Ignoring appUrlOpen - loading from history');
          return;
        }

        // Check if this URL has already been processed
        if (processedSharedFileUrls.has(event.url)) {
          console.log('URL already processed, ignoring appUrlOpen:', event.url);
          return;
        }

        // Check if we're already processing a shared file
        if (isProcessingSharedFileRef.current) {
          console.log('Already processing a shared file, ignoring new URL:', event.url);
          return;
        }

        // Set processing flag with timeout to prevent getting stuck
        isProcessingSharedFileRef.current = true;
        console.log('Set processing flag to true for URL:', event.url);

        // Mark this URL as processed to prevent re-processing
        processedSharedFileUrls.add(event.url);
        console.log('Marked URL as processed:', event.url);

        // Set a timeout to clear the processing flag if it gets stuck
        setTimeout(() => {
          if (isProcessingSharedFileRef.current) {
            console.log('Clearing stuck processing flag after timeout');
            isProcessingSharedFileRef.current = false;
          }
        }, 30000); // 30 second timeout

        try {
          // The URL will be in the format: chatvibeanalyzer://file/path/to/file
          // or file:///path/to/file
          const url = new URL(event.url);

          // Extract the file path from the URL
          const filePath = decodeURIComponent(url.pathname);

          // Check if it's a file URL or content URI
          if (url.protocol === 'file:' || url.hostname === 'file' || event.url.startsWith('content://')) {
            console.log('Processing file from URL:', filePath);

            // Extract the file name from the path
            let fileName = filePath.split('/').pop() || 'chat.txt';
            console.log('File name extracted from path:', fileName);

            // Skip opening the file with the default app to prevent popup
            console.log('Skipping opening file with default app to prevent popup');

            // Try multiple approaches to read the file
            let fileContent = null;
            let fileText = '';
            let success = false;

            // Normalize the file path for iOS
            const normalizedPath = getNormalizedFilePath(filePath);
            console.log('Normalized file path:', normalizedPath);

            // Platform-specific file reading approaches
            if (Capacitor.getPlatform() === 'ios') {
              try {
                console.log('Trying to read file directly with full path (iOS)');
                fileContent = await Filesystem.readFile({
                  path: event.url // Use the original URL directly
                });
                success = true;
                console.log('Successfully read file with full path (iOS)');
              } catch (iosError) {
                console.log('Failed to read with full path (iOS):', iosError);
              }
            } else if (Capacitor.getPlatform() === 'android') {
              // Android-specific handling for content:// URIs and file:// URIs
              try {
                console.log('Trying to read file with Android-specific handling');
                
                // For Android, try reading the file with the original URL
                // This handles both content:// and file:// URIs
                fileContent = await Filesystem.readFile({
                  path: event.url
                });
                success = true;
                console.log('Successfully read file with Android handling');
              } catch (androidError) {
                console.log('Failed to read with Android URL handling:', androidError);
                
                // Fallback: try with normalized path
                try {
                  console.log('Trying Android fallback with normalized path');
                  fileContent = await Filesystem.readFile({
                    path: normalizedPath
                  });
                  success = true;
                  console.log('Successfully read file with Android normalized path');
                } catch (androidFallbackError) {
                  console.log('Failed to read with Android normalized path:', androidFallbackError);
                }
              }
            }

            // Approach 1: Try to read using Filesystem API with Documents directory
            if (!success) {
              try {
                console.log('Trying to read file with Filesystem API (Documents directory)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath,
                  directory: Directory.Documents
                });
                success = true;
              } catch (error) {
                console.log('Failed to read with Documents directory:', error);
              }
            }

            // Approach 2: Try to read using Filesystem API with ExternalStorage directory
            if (!success) {
              try {
                console.log('Trying to read file with Filesystem API (ExternalStorage directory)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath,
                  directory: Directory.ExternalStorage
                });
                success = true;
              } catch (error) {
                console.log('Failed to read with ExternalStorage directory:', error);
              }
            }

            // Approach 3: Try to read using Filesystem API with Data directory
            if (!success) {
              try {
                console.log('Trying to read file with Filesystem API (Data directory)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath,
                  directory: Directory.Data
                });
                success = true;
              } catch (error) {
                console.log('Failed to read with Data directory:', error);
              }
            }

            // Approach 4: Try to read using Filesystem API with Cache directory
            if (!success) {
              try {
                console.log('Trying to read file with Filesystem API (Cache directory)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath,
                  directory: Directory.Cache
                });
                success = true;
              } catch (error) {
                console.log('Failed to read with Cache directory:', error);
              }
            }

            // Approach 5: Try to read using Filesystem API with absolute path
            if (!success) {
              try {
                console.log('Trying to read file with Filesystem API (absolute path)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath
                });
                success = true;
              } catch (error) {
                console.log('Failed to read with absolute path:', error);
              }
            }

            // Approach 6: For iOS, try with the exact original path (no normalization)
            if (!success && Capacitor.getPlatform() === 'ios') {
              try {
                console.log('Trying with exact original iOS path (no normalization)');
                fileContent = await Filesystem.readFile({
                  path: event.url
                });
                success = true;
                console.log('Successfully read file with exact iOS path');
              } catch (iosError) {
                console.error('Failed to read with exact iOS path:', iosError);
              }
            }

            // If we successfully read the file with Filesystem API
            if (success && fileContent) {
              try {
                // Check if this is a zip file first
                if (fileName.toLowerCase().endsWith('.zip')) {
                  console.log('Processing zip file from iOS file system');
                  
                  let zipData: ArrayBuffer;
                  
                  // Convert file content to ArrayBuffer for zip processing
                  if (typeof fileContent.data === 'string') {
                    // If it's base64 encoded, decode it to binary data
                    if (/^[A-Za-z0-9+/=]+$/.test(fileContent.data.substring(0, 100))) {
                      console.log('Converting base64 zip data to ArrayBuffer');
                      const binaryString = atob(fileContent.data);
                      const bytes = new Uint8Array(binaryString.length);
                      for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                      }
                      zipData = bytes.buffer;
                    } else {
                      // Convert string to ArrayBuffer
                      const encoder = new TextEncoder();
                      zipData = encoder.encode(fileContent.data).buffer;
                    }
                  } else if (fileContent.data instanceof ArrayBuffer) {
                    zipData = fileContent.data;
                  } else {
                    // Convert other types to ArrayBuffer
                    const blob = new Blob([fileContent.data as any]);
                    zipData = await blob.arrayBuffer();
                  }
                  
                  // Process the zip file and extract chat text
                  try {
                    fileText = await processZipFile(zipData, fileName);
                    fileName = '_chat.txt'; // Use standard chat filename
                    console.log('Successfully extracted text from iOS zip file, length:', fileText.length);
                  } catch (zipError) {
                    console.error('Error processing zip file from iOS:', zipError);
                    throw new Error(`Failed to extract chat from zip file: ${zipError instanceof Error ? zipError.message : String(zipError)}`);
                  }
                } else {
                  // Handle text files
                  if (typeof fileContent.data === 'string') {
                    // Check if it's a base64 string (only contains valid base64 characters)
                    if (/^[A-Za-z0-9+/=]+$/.test(fileContent.data.substring(0, 100))) {
                      console.log('Detected base64 encoded content, decoding with proper encoding detection...');
                      try {
                        // Use our utility function for proper encoding detection and handling
                        const decodedText = decodeBase64WithProperEncoding(fileContent.data);
                        console.log('Successfully decoded base64 content with proper encoding, length:', decodedText.length);

                        fileText = decodedText;

                        // If the file name doesn't already include _chat and it looks like WhatsApp format
                        // Use a more flexible regex that can handle special characters and different formats
                        // This regex now also detects the US format (MM/DD/YY, HH:MM), Turkish format (DD.MM.YYYY HH:MM - Sender: Message), and DD/MM/YYYY bracket format
                        // The backend now handles all format validation.
                        // We can remove the isWhatsAppFormat check.
                        if (!fileName.includes('_chat')) {
                           fileName = '_chat.txt';
                        }
                      } catch (decodeError) {
                        console.error('Error decoding base64 content:', decodeError);
                        // If decoding fails, use the original string
                        fileText = fileContent.data;
                      }
                    } else {
                      // Regular string content
                      fileText = fileContent.data;
                    }
                  } else if (typeof fileContent.data === 'object') {
                    // Handle blob or other object types
                    const blob = new Blob([fileContent.data as any]);
                    fileText = await blob.text();
                  } else {
                    // If it's a base64 string, decode it with proper encoding detection
                    try {
                      fileText = decodeBase64WithProperEncoding(fileContent.data as string);
                    } catch (decodeError) {
                      console.error('Error decoding base64 content:', decodeError);
                      fileText = fileContent.data as string;
                    }
                  }
                }

                console.log('Successfully read file, text length:', fileText.length);

                // Create a File object from the text for consistent handling
                console.log('Creating File object with filename:', fileName);
                const fileObj = new File([fileText], fileName, {
                  type: fileName.endsWith('.zip') ? 'application/zip' :
                        fileName.endsWith('.json') ? 'application/json' : 'text/plain'
                });
                console.log('File object created, size:', fileObj.size);

                // Process the file using the same function used for web uploads
                // This ensures the file is sent to the backend
                await processFile(fileObj, onFileProcessed);

                // Clear processing flag after successful processing
                isProcessingSharedFileRef.current = false;
                console.log('Cleared processing flag after successful file processing');

                toast.success('File processed successfully!');
                return; // Exit early if successful
              } catch (textError) {
                console.error('Error converting file content to text:', textError);
                success = false;
              }
            }

            // Approach 6: Fallback to fetch API
            if (!success) {
              try {
                console.log('Trying to fetch file from URL:', event.url);
                const response = await fetch(event.url);
                const blob = await response.blob();

                console.log('Successfully fetched file, size:', blob.size);

                // Handle zip files specifically
                if (fileName.toLowerCase().endsWith('.zip')) {
                  console.log('Processing fetched zip file');
                  try {
                    const zipData = await blob.arrayBuffer();
                    fileText = await processZipFile(zipData, fileName);
                    fileName = '_chat.txt'; // Use standard chat filename
                    console.log('Successfully extracted text from fetched zip file, length:', fileText.length);
                    
                    // Create a File object from the extracted text
                    const fileObj = new File([fileText], fileName, { type: 'text/plain' });
                    await processFile(fileObj, onFileProcessed);
                    
                    // Clear processing flag after successful processing
                    isProcessingSharedFileRef.current = false;
                    console.log('Cleared processing flag after successful zip file processing');
                    
                    toast.success('File processed successfully!');
                    return; // Exit early if successful
                  } catch (zipError) {
                    console.error('Error processing fetched zip file:', zipError);
                    throw new Error(`Failed to extract chat from zip file: ${zipError instanceof Error ? zipError.message : String(zipError)}`);
                  }
                } else {
                  // Handle text files
                  const file = new File([blob], fileName, { type: 'text/plain' });
                  await processFile(file, onFileProcessed);
                  
                  // Clear processing flag after successful processing
                  isProcessingSharedFileRef.current = false;
                  console.log('Cleared processing flag after successful text file processing');
                  
                  return; // Exit early if successful
                }
              } catch (fetchError) {
                console.error('Error fetching file:', fetchError);
              }
            }

            // Approach 7: Final fallback - show proper error message
            if (!success) {
              const errorMessage = fileName.toLowerCase().endsWith('.zip')
                ? 'Unable to extract chat data from the zip file. Please ensure the zip contains a valid WhatsApp chat export.'
                : 'Unable to read the file. Please try sharing the file again or check if it\'s a valid chat export.';
              
              console.error('All file reading approaches failed for:', fileName);
              
              // Clear processing flag on error
              isProcessingSharedFileRef.current = false;
              console.log('Cleared processing flag due to file reading error');
              
              toast.error(errorMessage);
              
              // Don't create mock files or use mock data - show proper error
              throw new Error(errorMessage);
            }
          }
        } catch (error) {
          console.error('Error processing file from URL:', error);
          
          // Clear processing flag on error
          isProcessingSharedFileRef.current = false;
          console.log('Cleared processing flag due to processing error');
          
          toast.error('Error processing file: ' + (error instanceof Error ? error.message : String(error)));
        }
      });

      console.log('Registered appUrlOpen listener');
    } else {
      console.log('Not in a Capacitor environment, skipping appUrlOpen listener');
    }
  } catch (error) {
    console.log('Error setting up appUrlOpen listener:', error);
    // Don't show a toast as this is expected in web environment
  }
};

/**
 * Process a file and extract its text content
 * @param file The file to process
 * @param onFileProcessed Callback function to be called when the file is processed
 */
const processFile = async (
  file: File,
  onFileProcessed: (text: string, fileName: string) => void
) => {
  try {
    // Check if it's a supported file type
    if (file.name.toLowerCase().endsWith('.zip') || file.name.toLowerCase().endsWith('.txt') || file.name.toLowerCase().endsWith('.json')) {
      let fileText = '';

      // For text files, read the content with proper encoding detection
      if (file.name.toLowerCase().endsWith('.txt')) {
        try {
          // Use our utility function for proper encoding detection
          fileText = await readFileWithProperEncoding(file);
          console.log('Text file content read with proper encoding, length:', fileText.length);
        } catch (readError) {
          console.error('Error reading text file:', readError);
          // Fallback to the standard method if our utility fails
          try {
            fileText = await file.text();
            console.log('Text file content read with fallback method, length:', fileText.length);
          } catch (fallbackError) {
            console.error('Error reading text file with fallback method:', fallbackError);
            // Continue with empty text
          }
        }
      } else if (file.name.toLowerCase().endsWith('.json')) {
        // JSON file processing - let backend handle conversion
        console.log('Processing as JSON file');
        try {
          // Just validate it's valid JSON, let backend handle conversion
          const jsonText = await file.text();
          JSON.parse(jsonText);
          fileText = jsonText;
          console.log('JSON file content validated, length:', fileText.length);
        } catch (parseError) {
          console.error('Invalid JSON file:', parseError);
          toast.error('Invalid JSON file. Please upload a valid JSON file.');
          return;
        }
      } else if (file.name.toLowerCase().endsWith('.zip')) {
        // For zip files, try to extract the chat text for display purposes
        try {
          const zipData = await file.arrayBuffer();
          const zip = await JSZip.loadAsync(zipData);

          // Find the WhatsApp chat export file (typically named with "_chat.txt")
          let chatFile = null;
          let chatFileName = '';

          // First, try to find a file with "_chat.txt" in the name (WhatsApp's standard format)
          for (const [path, zipEntry] of Object.entries(zip.files)) {
            if (path.toLowerCase().includes('_chat.txt') && !zipEntry.dir) {
              chatFile = zipEntry;
              chatFileName = path;
              break;
            }
          }

          // If no "_chat.txt" file found, look for any .txt file as fallback
          if (!chatFile) {
            for (const [path, zipEntry] of Object.entries(zip.files)) {
              if (path.toLowerCase().endsWith('.txt') && !zipEntry.dir) {
                chatFile = zipEntry;
                chatFileName = path;
                break;
              }
            }
          }

          if (chatFile) {
            // Extract the text content
            fileText = await chatFile.async('string');
            console.log('Extracted text from zip file, length:', fileText.length);
          } else {
            console.warn('No chat export (.txt file) found in the zip archive');
          }
        } catch (zipError) {
          console.error('Error extracting text from zip:', zipError);
          // Continue with empty text
        }
      }

      // Check if this is a WhatsApp chat format and rename if needed
      // Use a more flexible regex that can handle special characters and different formats
      // This regex now also detects the US format (MM/DD/YY, HH:MM) and Turkish format (DD.MM.YYYY HH:MM - Sender: Message)
      const isWhatsAppFormat = /(\[\d{4}-\d{2}-\d{2}.*?\d{1,2}:\d{2}.*?\]|\d{1,2}\/\d{1,2}\/\d{2},\s\d{1,2}:\d{2}|\d{1,2}\.\d{1,2}\.\d{2,4}\s\d{1,2}:\d{2}\s-\s)/.test(fileText.substring(0, 100));
      console.log('processFile: WhatsApp format detection - first 50 chars:', fileText.substring(0, 50).replace(/\n/g, '\\n'));
      console.log('processFile: WhatsApp format detected:', isWhatsAppFormat);

      if (fileText.length > 0 && !file.name.includes('_chat') && isWhatsAppFormat) {
        console.log('processFile: Detected WhatsApp chat format, using _chat.txt filename');
        // Call the callback with the WhatsApp chat name for better backend processing
        onFileProcessed(fileText, '_chat.txt');
        // Emit event for UploadSection to listen to
        window.dispatchEvent(new CustomEvent('fileProcessed', {
          detail: { text: fileText, fileName: '_chat.txt' }
        }));
      } else {
        // Call the callback function with the file text (may be empty if extraction failed)
        onFileProcessed(fileText, file.name);
        // Emit event for UploadSection to listen to
        window.dispatchEvent(new CustomEvent('fileProcessed', {
          detail: { text: fileText, fileName: file.name }
        }));
      }

      const fileType = file.name.toLowerCase().endsWith('.zip') ? 'Zip' :
                      file.name.toLowerCase().endsWith('.json') ? 'JSON' : 'Text';
      toast.success(`${fileType} file processed successfully!`);
    } else {
      toast.error('Unsupported file type. Please upload a .txt, .zip, or .json file.');
    }
  } catch (error) {
    console.error('Error processing file:', error);
    toast.error('Error processing file: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Check if the app was opened with a file
 * This should be called when the app starts
 * @param onFileProcessed Callback function to be called when a file is processed
 */
export const checkInitialFile = async (
  onFileProcessed: (text: string, fileName: string) => void,
  isProcessingSharedFileRef: React.MutableRefObject<boolean>,
  isLoadingFromHistory: boolean
) => {
  try {
    // Check if we're in a Capacitor environment
    if (typeof (window as any).Capacitor === 'undefined') {
      console.log('📱 Not in a Capacitor environment, skipping initial file check');
      return;
    }

    console.log('📱 Platform:', Capacitor.getPlatform());
    console.log('📱 Checking for initial file...');

    try {
      // Get the initial URL that opened the app
      const { url } = await App.getLaunchUrl();
      console.log('📱 getLaunchUrl result:', { url });

      if (url) {
        console.log('📱 App launched with URL:', url);
        console.log('📱 URL type:', typeof url);

        // Check if we're loading from history - if so, skip processing
        if (isLoadingFromHistory) {
          console.log('Ignoring initial file check - loading from history');
          return;
        }

        // Check if this URL has already been processed
        if (processedSharedFileUrls.has(url)) {
          console.log('URL already processed, ignoring initial URL:', url);
          return;
        }

        // Check if we're already processing a shared file
        if (isProcessingSharedFileRef.current) {
          console.log('Already processing a shared file, ignoring initial URL:', url);
          return;
        }

        // Set processing flag with timeout to prevent getting stuck
        isProcessingSharedFileRef.current = true;
        console.log('Set processing flag to true for initial URL:', url);

        // Mark this URL as processed to prevent re-processing
        processedSharedFileUrls.add(url);
        console.log('Marked URL as processed:', url);

        // Set a timeout to clear the processing flag if it gets stuck
        setTimeout(() => {
          if (isProcessingSharedFileRef.current) {
            console.log('Clearing stuck processing flag after timeout (initial file)');
            isProcessingSharedFileRef.current = false;
          }
        }, 30000); // 30 second timeout

        // Process the file using the same logic as in initFileHandling
        const parsedUrl = new URL(url);
        const filePath = decodeURIComponent(parsedUrl.pathname);

        if (parsedUrl.protocol === 'file:' || parsedUrl.hostname === 'file') {
          console.log('Processing initial file:', filePath);

          // Extract the file name from the path
          let fileName = filePath.split('/').pop() || 'chat.txt';
          console.log('File name extracted from path:', fileName);

          // Skip opening the file with the default app to prevent popup
          console.log('Skipping opening file with default app to prevent popup');

          // Try multiple approaches to read the file
          let fileContent = null;
          let fileText = '';
          let success = false;

          // Normalize the file path for iOS
          const normalizedPath = getNormalizedFilePath(filePath);
          console.log('Normalized file path:', normalizedPath);

          // Platform-specific file reading approaches
          if (Capacitor.getPlatform() === 'ios') {
            try {
              console.log('Trying to read file directly with full path (iOS)');
              fileContent = await Filesystem.readFile({
                path: url // Use the original URL directly
              });
              success = true;
              console.log('Successfully read file with full path (iOS)');
            } catch (iosError) {
              console.log('Failed to read with full path (iOS):', iosError);
            }
          } else if (Capacitor.getPlatform() === 'android') {
            // Android-specific handling for content:// URIs and file:// URIs
            try {
              console.log('Trying to read file with Android-specific handling (initial)');
              
              // For Android, try reading the file with the original URL
              // This handles both content:// and file:// URIs
              fileContent = await Filesystem.readFile({
                path: url
              });
              success = true;
              console.log('Successfully read file with Android handling (initial)');
            } catch (androidError) {
              console.log('Failed to read with Android URL handling (initial):', androidError);
              
              // Fallback: try with normalized path
              try {
                console.log('Trying Android fallback with normalized path (initial)');
                fileContent = await Filesystem.readFile({
                  path: normalizedPath
                });
                success = true;
                console.log('Successfully read file with Android normalized path (initial)');
              } catch (androidFallbackError) {
                console.log('Failed to read with Android normalized path (initial):', androidFallbackError);
              }
            }
          }

          // Approach 1: Try to read using Filesystem API with Documents directory
          if (!success) {
            try {
              console.log('Trying to read file with Filesystem API (Documents directory)');
              fileContent = await Filesystem.readFile({
                path: normalizedPath,
                directory: Directory.Documents
              });
              success = true;
            } catch (error) {
              console.log('Failed to read with Documents directory:', error);
            }
          }

          // Approach 2: Try to read using Filesystem API with ExternalStorage directory
          if (!success) {
            try {
              console.log('Trying to read file with Filesystem API (ExternalStorage directory)');
              fileContent = await Filesystem.readFile({
                path: normalizedPath,
                directory: Directory.ExternalStorage
              });
              success = true;
            } catch (error) {
              console.log('Failed to read with ExternalStorage directory:', error);
            }
          }

          // Approach 3: Try to read using Filesystem API with Data directory
          if (!success) {
            try {
              console.log('Trying to read file with Filesystem API (Data directory)');
              fileContent = await Filesystem.readFile({
                path: normalizedPath,
                directory: Directory.Data
              });
              success = true;
            } catch (error) {
              console.log('Failed to read with Data directory:', error);
            }
          }

          // Approach 4: Try to read using Filesystem API with Cache directory
          if (!success) {
            try {
              console.log('Trying to read file with Filesystem API (Cache directory)');
              fileContent = await Filesystem.readFile({
                path: normalizedPath,
                directory: Directory.Cache
              });
              success = true;
            } catch (error) {
              console.log('Failed to read with Cache directory:', error);
            }
          }

          // Approach 5: Try to read using Filesystem API with absolute path
          if (!success) {
            try {
              console.log('Trying to read file with Filesystem API (absolute path)');
              fileContent = await Filesystem.readFile({
                path: normalizedPath
              });
              success = true;
            } catch (error) {
              console.log('Failed to read with absolute path:', error);
            }
          }

          // Approach 6: For iOS, try with the exact original path (no normalization)
          if (!success && Capacitor.getPlatform() === 'ios') {
            try {
              console.log('Trying with exact original iOS path (no normalization)');
              fileContent = await Filesystem.readFile({
                path: url
              });
              success = true;
              console.log('Successfully read file with exact iOS path');
            } catch (iosError) {
              console.error('Failed to read with exact iOS path:', iosError);
            }
          }

          // Approach 7: For iOS, try with the path without the file:// prefix
          if (!success && Capacitor.getPlatform() === 'ios' && url.startsWith('file://')) {
            try {
              const strippedPath = url.substring(7); // Remove 'file://'
              console.log('Trying with stripped path:', strippedPath);
              fileContent = await Filesystem.readFile({
                path: strippedPath
              });
              success = true;
              console.log('Successfully read file with stripped path');
            } catch (strippedError) {
              console.error('Failed to read with stripped path:', strippedError);
            }
          }

          // If we successfully read the file with Filesystem API
          if (success && fileContent) {
            try {
              // Check if this is a zip file first
              if (fileName.toLowerCase().endsWith('.zip')) {
                console.log('Processing zip file from initial file check');
                
                let zipData: ArrayBuffer;
                
                // Convert file content to ArrayBuffer for zip processing
                if (typeof fileContent.data === 'string') {
                  // If it's base64 encoded, decode it to binary data
                  if (/^[A-Za-z0-9+/=]+$/.test(fileContent.data.substring(0, 100))) {
                    console.log('Converting base64 zip data to ArrayBuffer');
                    const binaryString = atob(fileContent.data);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                      bytes[i] = binaryString.charCodeAt(i);
                    }
                    zipData = bytes.buffer;
                  } else {
                    // Convert string to ArrayBuffer
                    const encoder = new TextEncoder();
                    zipData = encoder.encode(fileContent.data).buffer;
                  }
                } else if (fileContent.data instanceof ArrayBuffer) {
                  zipData = fileContent.data;
                } else {
                  // Convert other types to ArrayBuffer
                  const blob = new Blob([fileContent.data as any]);
                  zipData = await blob.arrayBuffer();
                }
                
                // Process the zip file and extract chat text
                try {
                  fileText = await processZipFile(zipData, fileName);
                  fileName = '_chat.txt'; // Use standard chat filename
                  console.log('Successfully extracted text from initial zip file, length:', fileText.length);
                } catch (zipError) {
                  console.error('Error processing zip file from initial check:', zipError);
                  throw new Error(`Failed to extract chat from zip file: ${zipError instanceof Error ? zipError.message : String(zipError)}`);
                }
              } else {
                // Handle text files
                if (typeof fileContent.data === 'string') {
                  // Check if it's a base64 string (only contains valid base64 characters)
                  if (/^[A-Za-z0-9+/=]+$/.test(fileContent.data.substring(0, 100))) {
                    console.log('Detected base64 encoded content, decoding...');
                    try {
                      const decoded = atob(fileContent.data);
                      console.log('Successfully decoded base64 content, new length:', decoded.length);
                      console.log('Decoded content sample (first 200 chars):', decoded.substring(0, 200));

                      // Fix encoding issues only in timestamps while preserving all other special characters
                      const fixedText = decoded
                        // Fix special characters in timestamps only
                        .replace(/\[(\d{4}-\d{2}-\d{2}|\d{2}\.\d{2}\.\d{2}),?\s+\d{1,2}:\d{2}(:\d{2})?â¯(AM|PM)\]/g, (match) => {
                          return match.replace('â¯', ' ');
                        });

                      console.log('Fixed encoding issues in decoded text');
                      console.log('Fixed text sample (first 200 chars):', fixedText.substring(0, 200));

                      fileText = fixedText;

                      // If the file name doesn't already include _chat and it looks like WhatsApp format
                      // Use a more flexible regex that can handle special characters and different formats
                      // This regex now also detects the US format (MM/DD/YY, HH:MM) and Turkish format (DD.MM.YYYY HH:MM - Sender: Message)
                      const isWhatsAppFormat = /(\[\d{4}-\d{2}-\d{2}.*?\d{1,2}:\d{2}.*?\]|\d{1,2}\/\d{1,2}\/\d{2},\s\d{1,2}:\d{2}|\d{1,2}\.\d{1,2}\.\d{2,4}\s\d{1,2}:\d{2}\s-\s)/.test(decoded.substring(0, 100));
                      console.log('Decoded content - first 50 chars:', decoded.substring(0, 50).replace(/\n/g, '\\n'));
                      console.log('Decoded content appears to be WhatsApp format:', isWhatsAppFormat);

                      if (!fileName.includes('_chat') && isWhatsAppFormat) {
                        console.log('Decoded content appears to be a WhatsApp chat, will use _chat.txt filename');
                        fileName = '_chat.txt';
                      }
                    } catch (decodeError) {
                      console.error('Error decoding base64 content:', decodeError);
                      // If decoding fails, use the original string
                      fileText = fileContent.data;
                    }
                  } else {
                    // Regular string content
                    fileText = fileContent.data;
                  }
                } else if (typeof fileContent.data === 'object') {
                  // Handle blob or other object types
                  const blob = new Blob([fileContent.data as any]);
                  fileText = await blob.text();
                } else {
                  // If it's a base64 string, decode it
                  try {
                    fileText = atob(fileContent.data as string);
                  } catch (decodeError) {
                    console.error('Error decoding base64 content:', decodeError);
                    fileText = fileContent.data as string;
                  }
                }
              }

              console.log('Successfully read file, text length:', fileText.length);

              // Create a File object from the text for consistent handling
              console.log('Creating File object with filename:', fileName);
              const fileObj = new File([fileText], fileName, {
                type: fileName.endsWith('.zip') ? 'application/zip' : 'text/plain'
              });
              console.log('File object created, size:', fileObj.size);

              // Process the file using the same function used for web uploads
              // This ensures the file is sent to the backend
              await processFile(fileObj, onFileProcessed);

              // Clear processing flag after successful processing
              isProcessingSharedFileRef.current = false;
              console.log('Cleared processing flag after successful initial file processing');

              toast.success('File processed successfully!');
              return; // Exit early if successful
            } catch (textError) {
              console.error('Error converting file content to text:', textError);
              success = false;
            }
          }

          // Approach 6: Fallback to fetch API
          if (!success) {
            try {
              console.log('Trying to fetch file from URL:', url);
              const response = await fetch(url);
              const blob = await response.blob();

              console.log('Successfully fetched file, size:', blob.size);

              // Handle zip files specifically
              if (fileName.toLowerCase().endsWith('.zip')) {
                console.log('Processing fetched zip file in initial check');
                try {
                  const zipData = await blob.arrayBuffer();
                  fileText = await processZipFile(zipData, fileName);
                  fileName = '_chat.txt'; // Use standard chat filename
                  console.log('Successfully extracted text from fetched zip file in initial check, length:', fileText.length);
                  
                  // Create a File object from the extracted text
                  const fileObj = new File([fileText], fileName, { type: 'text/plain' });
                  await processFile(fileObj, onFileProcessed);
                  
                  // Clear processing flag after successful processing
                  isProcessingSharedFileRef.current = false;
                  console.log('Cleared processing flag after successful initial zip file processing');
                  
                  toast.success('File processed successfully!');
                  return; // Exit early if successful
                } catch (zipError) {
                  console.error('Error processing fetched zip file in initial check:', zipError);
                  throw new Error(`Failed to extract chat from zip file: ${zipError instanceof Error ? zipError.message : String(zipError)}`);
                }
              } else {
                // Handle text files
                const file = new File([blob], fileName, { type: 'text/plain' });
                await processFile(file, onFileProcessed);
                
                // Clear processing flag after successful processing
                isProcessingSharedFileRef.current = false;
                console.log('Cleared processing flag after successful initial text file processing');
                
                return; // Exit early if successful
              }
            } catch (fetchError) {
              console.error('Error fetching file:', fetchError);
            }
          }

          // Approach 7: Final fallback - show proper error message
          if (!success) {
            const errorMessage = fileName.toLowerCase().endsWith('.zip')
              ? 'Unable to extract chat data from the zip file. Please ensure the zip contains a valid WhatsApp chat export.'
              : 'Unable to read the file. Please try sharing the file again or check if it\'s a valid chat export.';
            
            console.error('All file reading approaches failed for initial file check:', fileName);
            
            // Clear processing flag on error
            isProcessingSharedFileRef.current = false;
            console.log('Cleared processing flag due to initial file reading error');
            
            toast.error(errorMessage);
            
            // Don't create mock files or use mock data - show proper error
            throw new Error(errorMessage);
          }
        }
      } else {
        console.log('No initial URL found');
      }
    } catch (appError) {
      // This is likely an error with the Capacitor App plugin
      console.log('App.getLaunchUrl() not available or failed:', appError);
      // Don't throw, just log and continue
    }
  } catch (error) {
    // Only log the error, don't show a toast as this is expected in web environment
    console.log('Initial file check skipped:', error);
  }
};

/**
 * Open a file with the default app
 * This function is now disabled to prevent files from opening as popups
 * @param filePath The path to the file to open
 */
const openFileWithDefaultApp = async (filePath: string): Promise<void> => {
  // Skip opening files to prevent popups
  console.log('File opening with default app is disabled to prevent popups');
  return;
};

/**
 * Analyze WhatsApp chat format to help with debugging
 * @param chatText The chat text to analyze
 */
const analyzeWhatsAppChatFormat = (chatText: string): void => {
  if (!chatText || chatText.trim().length < 10) {
    console.log('WhatsApp Format Analysis: Text too short to analyze');
    return;
  }

  console.log('WhatsApp Format Analysis:');
  console.log('------------------------');

  // Check for common WhatsApp export patterns
  const lines = chatText.split('\n').filter(line => line.trim().length > 0);
  const sampleSize = Math.min(10, lines.length);

  console.log(`Analyzing first ${sampleSize} non-empty lines:`);

  // Check for different timestamp formats
  const bracketTimestampFormat = /^\[\d{4}-\d{2}-\d{2},?\s+\d{1,2}:\d{2}(?::\d{2})?\s*(?:[AP]M)?\]/;
  const usTimestampFormat = /^\d{1,2}\/\d{1,2}\/\d{2,4},\s+\d{1,2}:\d{2}(?::\d{2})?\s*(?:[AP]M)?/;
  const dashTimestampFormat = /^\d{1,2}-\d{1,2}-\d{2,4}\s+\d{1,2}:\d{2}(?::\d{2})?\s*(?:[AP]M)?/;
  const turkishTimestampFormat = /^\d{1,2}\.\d{1,2}\.\d{2,4}\s+\d{1,2}:\d{2}\s-\s/;
  const ddMmBracketFormat = /^\[\d{1,2}\/\d{1,2}\/\d{4},\s\d{1,2}:\d{2}:\d{2}\]/;

  let bracketFormatCount = 0;
  let usFormatCount = 0;
  let dashFormatCount = 0;
  let turkishFormatCount = 0;
  let ddMmBracketFormatCount = 0;
  let colonCount = 0;
  let messageLineCount = 0;

  for (let i = 0; i < sampleSize; i++) {
    const line = lines[i];
    console.log(`Line ${i+1}: ${line.substring(0, 100)}`);

    if (bracketTimestampFormat.test(line)) bracketFormatCount++;
    if (usTimestampFormat.test(line)) usFormatCount++;
    if (dashTimestampFormat.test(line)) dashFormatCount++;
    if (turkishTimestampFormat.test(line)) turkishFormatCount++;
    if (ddMmBracketFormat.test(line)) ddMmBracketFormatCount++;
    if (line.includes(':')) colonCount++;

    // Check if this looks like a message line (has timestamp and colon)
    if ((bracketTimestampFormat.test(line) || usTimestampFormat.test(line) || dashTimestampFormat.test(line) || turkishTimestampFormat.test(line) || ddMmBracketFormat.test(line))
        && line.includes(':')) {
      messageLineCount++;
    }
  }

  console.log('Format detection results:');
  console.log(`- Bracket timestamp format [YYYY-MM-DD, HH:MM]: ${bracketFormatCount} lines`);
  console.log(`- US timestamp format MM/DD/YY, HH:MM: ${usFormatCount} lines`);
  console.log(`- Dash timestamp format DD-MM-YY HH:MM: ${dashFormatCount} lines`);
  console.log(`- Turkish timestamp format DD.MM.YYYY HH:MM - : ${turkishFormatCount} lines`);
  console.log(`- DD/MM bracket format [DD/MM/YYYY, HH:MM:SS]: ${ddMmBracketFormatCount} lines`);
  console.log(`- Lines containing colons: ${colonCount} lines`);
  console.log(`- Likely message lines: ${messageLineCount} lines`);

  // Determine the most likely format
  if (ddMmBracketFormatCount > bracketFormatCount && ddMmBracketFormatCount > usFormatCount && ddMmBracketFormatCount > dashFormatCount && ddMmBracketFormatCount > turkishFormatCount) {
    console.log('Conclusion: Most likely using DD/MM bracket timestamp format [DD/MM/YYYY, HH:MM:SS]');
  } else if (turkishFormatCount > bracketFormatCount && turkishFormatCount > usFormatCount && turkishFormatCount > dashFormatCount) {
    console.log('Conclusion: Most likely using Turkish timestamp format DD.MM.YYYY HH:MM -');
  } else if (bracketFormatCount > usFormatCount && bracketFormatCount > dashFormatCount) {
    console.log('Conclusion: Most likely using bracket timestamp format [YYYY-MM-DD, HH:MM]');
  } else if (usFormatCount > bracketFormatCount && usFormatCount > dashFormatCount) {
    console.log('Conclusion: Most likely using US timestamp format MM/DD/YY, HH:MM');
  } else if (dashFormatCount > bracketFormatCount && dashFormatCount > usFormatCount) {
    console.log('Conclusion: Most likely using dash timestamp format DD-MM-YY HH:MM');
  } else {
    console.log('Conclusion: Unable to determine timestamp format with confidence');
  }

  console.log('------------------------');
};

/**
 * Get the correct file path for iOS
 * @param filePath The original file path
 * @returns The normalized file path
 */
const getNormalizedFilePath = (filePath: string): string => {
  // Handle file:// protocol
  if (filePath.startsWith('file://')) {
    return decodeURIComponent(filePath);
  }

  // If it's already a decoded path, return it as is
  return filePath;
};

/**
 * Validate if a file is a proper zip file
 * @param data The file data as ArrayBuffer or Uint8Array
 * @returns True if the file is a valid zip file
 */
const validateZipFile = (data: ArrayBuffer | Uint8Array): boolean => {
  try {
    const bytes = new Uint8Array(data);
    // Check for ZIP file signature (PK\x03\x04 or PK\x05\x06 or PK\x07\x08)
    if (bytes.length < 4) return false;
    
    return (bytes[0] === 0x50 && bytes[1] === 0x4b &&
            (bytes[2] === 0x03 && bytes[3] === 0x04)) || // Local file header
           (bytes[0] === 0x50 && bytes[1] === 0x4b &&
            (bytes[2] === 0x05 && bytes[3] === 0x06)) || // Central directory
           (bytes[0] === 0x50 && bytes[1] === 0x4b &&
            (bytes[2] === 0x07 && bytes[3] === 0x08));   // Spanning signature
  } catch (error) {
    console.error('Error validating zip file:', error);
    return false;
  }
};

/**
 * Process a zip file and extract the chat text
 * @param data The zip file data as ArrayBuffer
 * @param fileName The original file name for logging
 * @returns The extracted chat text
 */
const processZipFile = async (data: ArrayBuffer, fileName: string): Promise<string> => {
  try {
    console.log(`Processing zip file: ${fileName}, size: ${data.byteLength} bytes`);
    
    // Validate that this is actually a zip file
    if (!validateZipFile(data)) {
      throw new Error('File does not appear to be a valid zip archive');
    }
    
    // Load the zip file
    const zip = await JSZip.loadAsync(data);
    console.log(`Zip file loaded successfully, contains ${Object.keys(zip.files).length} files`);
    
    // Log all files in the zip for debugging
    const fileList = Object.keys(zip.files).filter(path => !zip.files[path].dir);
    console.log('Files in zip archive:', fileList);

    // Find the WhatsApp chat export file (typically named with "_chat.txt")
    let chatFile = null;
    let chatFileName = '';

    // First, try to find a file with "_chat.txt" in the name (WhatsApp's standard format)
    for (const [path, zipEntry] of Object.entries(zip.files)) {
      if (path.toLowerCase().includes('_chat.txt') && !zipEntry.dir) {
        chatFile = zipEntry;
        chatFileName = path;
        console.log(`Found WhatsApp chat file: ${path}`);
        break;
      }
    }

    // If no "_chat.txt" file found, look for any .txt file as fallback
    if (!chatFile) {
      console.log('No _chat.txt file found, looking for any .txt file');
      for (const [path, zipEntry] of Object.entries(zip.files)) {
        if (path.toLowerCase().endsWith('.txt') && !zipEntry.dir) {
          chatFile = zipEntry;
          chatFileName = path;
          console.log(`Found text file: ${path}`);
          break;
        }
      }
    }

    if (!chatFile) {
      throw new Error(`No chat export (.txt file) found in the zip archive. Files found: ${fileList.join(', ')}`);
    }

    // Extract the text content
    console.log(`Extracting text content from: ${chatFileName}`);
    const chatText = await chatFile.async('string');
    console.log(`Successfully extracted text content, length: ${chatText.length} characters`);
    
    // Log a sample of the extracted content for debugging
    console.log('Extracted content sample (first 200 chars):', chatText.substring(0, 200));
    
    return chatText;
  } catch (error) {
    console.error('Error processing zip file:', error);
    throw new Error(`Failed to extract chat from zip file: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Handle files shared from other apps
 * This function is called when a file is shared from another app
 */
const handleSharedFile = async (file: File): Promise<string> => {
  try {
    console.log(`Processing shared file: ${file.name}, type: ${file.type}, size: ${file.size}`);
    
    // Check if it's a zip file
    if (file.name.toLowerCase().endsWith('.zip')) {
      // Process zip file using the enhanced zip processor
      const zipData = await file.arrayBuffer();
      return await processZipFile(zipData, file.name);
    } else if (file.name.toLowerCase().endsWith('.txt')) {
      // Regular text file processing
      console.log('Processing as text file');
      return await file.text();
    } else if (file.name.toLowerCase().endsWith('.json')) {
      // JSON file processing - let backend handle conversion
      console.log('Processing as JSON file');
      const jsonText = await file.text();
      try {
        // Just validate it's valid JSON, let backend handle conversion
        JSON.parse(jsonText);
        return jsonText;
      } catch (parseError) {
        console.error('Invalid JSON file:', parseError);
        throw new Error('Invalid JSON file. Please upload a valid JSON file.');
      }
    } else {
      throw new Error('Unsupported file type. Please upload a .txt, .zip, or .json file.');
    }
  } catch (error) {
    console.error('Error processing shared file:', error);
    throw error;
  }
};

const FileHandler: React.FC = () => {
  const navigate = useNavigate();
  const { 
    state, 
    setState, 
    showSubscriptionModal, 
    isDailyLimitDialogOpen, 
    showDailyLimitDialog, 
    hideDailyLimitDialog,
    setFileProcessed 
  } = useAppState();
  const { analysisData, setAnalysisData, isLoadingFromHistory } = useAnalysis();

  // Function to save analysis to history using passed data (doesn't rely on React state)
  const saveAnalysisToHistory = async (chatText: string, fileName: string | undefined, analysisData: any) => {
    console.log('⚡️  [log] - saveAnalysisToHistory called with:', {
      chatTextLength: chatText?.length || 0,
      fileName,
      hasAnalysisData: !!analysisData,
      analysisDataKeys: analysisData ? Object.keys(analysisData) : [],
      hasResult: !!analysisData?.result,
      resultKeys: analysisData?.result ? Object.keys(analysisData.result) : [],
      analysisType: analysisData?.analysisType,
      selectedChatType: analysisData?.selectedChatType
    });
    
    // Comprehensive validation
    if (!analysisData) {
      console.error('⚡️  [log] - No analysis data object provided');
      return;
    }
    
    if (!analysisData.result) {
      console.error('⚡️  [log] - No analysis result in data object');
      return;
    }
    
    if (!chatText || chatText.length === 0) {
      console.error('⚡️  [log] - No chat text provided for saving');
      return;
    }
    
    console.log('⚡️  [log] - All validations passed, proceeding with save');
    
    if (!analysisData?.result) {
      console.log('⚡️  [log] - No analysis data to save');
      return;
    }

    try {
      console.log('⚡️  [log] - Starting analysis save process...');
      
      // Extract metadata from chat text
      const messageCount = extractMessageCount(chatText);
      const participantCount = extractParticipantCount(chatText);
      
      console.log('⚡️  [log] - Extracted metadata:', { messageCount, participantCount });
      
      // Generate hash for the chat
      const chatHash = await generateChatHash(chatText);
      const firstTenLines = extractFirstTenLines(chatText);
      
      console.log('⚡️  [log] - Generated chat hash:', chatHash);
      
      const analysisEntry = {
        title: fileName || analysisData.fileName || 'Chat Analysis',
        analysisJson: analysisData.result,
        analysisType: analysisData.analysisType || 'group',
        selectedChatType: analysisData.selectedChatType || 'group',
        originalFileName: analysisData.fileName,
        messageCount: messageCount,
        participants: participantCount,
        chatHash: chatHash
      };
      
      console.log('⚡️  [log] - Saving analysis entry:', {
        title: analysisEntry.title,
        analysisType: analysisEntry.analysisType,
        messageCount: analysisEntry.messageCount,
        participants: analysisEntry.participants,
        hasResult: !!analysisEntry.analysisJson
      });
      
      const savedAnalysis = await saveAnalysis(analysisEntry);
      
      console.log('⚡️  [log] - Analysis saved with ID:', savedAnalysis.id);
      
      // Store the hash mapping
      await storeChatHash(chatHash, savedAnalysis.id, firstTenLines);
      
      console.log('⚡️  [log] - Analysis saved successfully with hash:', chatHash);
      
      if (isDevelopmentMode()) {
        hapticFeedback.success();
        toast.dev('Analysis saved to history');
      }
      
      // Return success indicator
      return { success: true, id: savedAnalysis.id, hash: chatHash };
      
    } catch (error) {
      console.error('⚡️  [log] - Failed to save analysis:', error);
      
      // Enhanced error logging
      if (error instanceof Error) {
        console.error('⚡️  [log] - Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }
      
      if (isDevelopmentMode()) {
        hapticFeedback.error();
        toast.dev('Failed to save analysis to history');
      }
      
      // Show user-friendly error message
      toast.error('Failed to save analysis to history. Your analysis is still available but may not persist.');
      
      // Return error indicator
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Function to save analysis to history (legacy - depends on React state)
  const saveCurrentAnalysis = async (chatText: string, fileName?: string) => {
    console.log('⚡️  [log] - saveCurrentAnalysis called with:', {
      chatTextLength: chatText?.length || 0,
      fileName,
      hasAnalysisData: !!analysisData,
      analysisDataKeys: analysisData ? Object.keys(analysisData) : [],
      hasResult: !!analysisData?.result,
      resultKeys: analysisData?.result ? Object.keys(analysisData.result) : [],
      analysisType: analysisData?.analysisType,
      selectedChatType: analysisData?.selectedChatType
    });
    
    if (!analysisData?.result) {
      console.log('⚡️  [log] - No analysis data to save');
      return;
    }

    await saveAnalysisToHistory(chatText, fileName, analysisData);

  };

  // Helper function to detect if error is specifically a daily limit error
  const isDailyLimitError = (error: any): boolean => {
    const errorMessage = error.message?.toLowerCase() || '';
    console.log('🔍 Checking if daily limit error:', errorMessage);
    console.log('🔍 Full error object:', error);
    
    const isDailyLimit = errorMessage.includes('daily credit limit') || 
                        errorMessage.includes('daily limit') ||
                        errorMessage.includes('daily credit limit reached') ||
                        errorMessage.includes('analysis quota exceeded') ||
                        errorMessage.includes('quota exceeded');
    
    console.log('🎯 Daily limit detection result:', isDailyLimit);
    return isDailyLimit;
  };
  
  // Add processing flag to prevent duplicate file processing
  const isProcessingSharedFileRef = useRef<boolean>(false);
  
  // Error dialog state
  const [errorDialog, setErrorDialog] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
  }>({
    isOpen: false,
    title: '',
    message: ''
  });

  useEffect(() => {
    // **DEFENSIVE CLEANUP** - Ensure clean state on component mount
    console.log('🎯 FileHandler: Component mounting - performing defensive cleanup');
    console.log('🎯 Platform:', Capacitor.getPlatform());
    console.log('🎯 Capacitor available:', typeof (window as any).Capacitor !== 'undefined');
    if (isProcessingSharedFileRef.current && !isLoadingFromHistory) {
      console.log('FileHandler: Clearing stuck processing flag on mount');
      isProcessingSharedFileRef.current = false;
    }

    // Initialize file handling
    const handleProcessedFile = async (text: string, fileName: string) => {
      const performAnalysis = async () => {
        try {
          // Check if we're loading from history - if so, skip all processing
          if (isLoadingFromHistory) {
            console.log('Skipping file processing - loading from history');
            isProcessingSharedFileRef.current = false;
            return;
          }

          // Clear processing flag when starting manual analysis
          // This ensures manual uploads can proceed even if shared file processing was interrupted
          if (isProcessingSharedFileRef.current) {
            console.log('Clearing processing flag for manual file processing');
            isProcessingSharedFileRef.current = false;
          }
          console.log('Processing file:', fileName);
          console.log('Text length:', text.length);

          // **PROACTIVE QUOTA CHECKING** - Check before starting analysis
          console.log('FileHandler: Checking quota before starting analysis...');
          try {
            const quotaCheck = await canAnalyze();
            
            if (!quotaCheck.allowed) {
              console.log('FileHandler: Quota check failed:', quotaCheck.message);
              
              // Determine if this is a subscription issue or daily limit issue
              const quotaStatus = quotaCheck.quota;
              
              if (quotaStatus?.tier === 'free' && quotaStatus?.remaining === 0) {
                // Scenario 1: No subscription (free tier, no credits left)
                console.log('FileHandler: No subscription - showing subscription modal');
                isProcessingSharedFileRef.current = false;
                
                showSubscriptionModal(async () => {
                  try {
                    // Retry analysis after successful subscription
                    console.log('FileHandler: Retrying analysis after subscription success');
                    await performAnalysis();
                  } catch (retryError) {
                    console.error('FileHandler: Analysis retry failed after subscription:', retryError);
                    setState(AnalysisState.UPLOAD);
                    toast.error('Analysis failed after subscription. Please try again.');
                  }
                });
                return;
              } else if (quotaStatus?.tier === 'premium' && quotaStatus?.remaining === 0) {
                // Scenario 2: Has subscription but no daily credits left
                console.log('FileHandler: Has subscription but no daily credits - showing daily limit dialog');
                isProcessingSharedFileRef.current = false;
                showDailyLimitDialog();
                return;
              } else {
                // Fallback for other quota issues
                console.log('FileHandler: Other quota issue - showing subscription modal');
                isProcessingSharedFileRef.current = false;
                
                showSubscriptionModal(async () => {
                  try {
                    console.log('FileHandler: Retrying analysis after subscription success (fallback)');
                    await performAnalysis();
                  } catch (retryError) {
                    console.error('FileHandler: Analysis retry failed after subscription (fallback):', retryError);
                    setState(AnalysisState.UPLOAD);
                    toast.error('Analysis failed after subscription. Please try again.');
                  }
                });
                return;
              }
            }
            
            console.log('FileHandler: Quota check passed - proceeding with analysis');
          } catch (quotaError) {
            console.error('FileHandler: Error checking quota:', quotaError);
            // If quota check fails, show subscription modal as fallback
            isProcessingSharedFileRef.current = false;
            
            showSubscriptionModal(async () => {
              try {
                console.log('FileHandler: Retrying analysis after quota check error');
                await performAnalysis();
              } catch (retryError) {
                console.error('FileHandler: Analysis retry failed after quota error:', retryError);
                setState(AnalysisState.UPLOAD);
                toast.error('Analysis failed. Please try again.');
              }
            });
            return;
          }

          // Log a sample of the text content for debugging
          if (text.length > 0) {
            console.log('Text content sample (first 200 chars):', text.substring(0, 200));
          }

          // Create a File object from the text
          let file = new File([text], fileName, {
            type: fileName.endsWith('.zip') ? 'application/zip' : 'text/plain'
          });
          console.log('Created file object with name:', file.name, 'size:', file.size);

          // Show loading state
          setState(AnalysisState.ANALYZING);

          // Check if the file is a WhatsApp chat format
          // If it's from an external source, it might need special handling
          // Use a more flexible regex that can handle special characters and different formats
          // This regex now also detects the US format (MM/DD/YY, HH:MM) and Turkish format (DD.MM.YYYY HH:MM - Sender: Message)
          const isWhatsAppFormat = /(\[\d{4}-\d{2}-\d{2}.*?\d{1,2}:\d{2}.*?\]|\d{1,2}\/\d{1,2}\/\d{2},\s\d{1,2}:\d{2}|\d{1,2}\.\d{1,2}\.\d{2,4}\s\d{1,2}:\d{2}\s-\s)/.test(text.substring(0, 100));
          console.log('WhatsApp format detection - first 50 chars:', text.substring(0, 50).replace(/\n/g, '\\n'));
          console.log('WhatsApp format detected:', isWhatsAppFormat);

          if (text.length > 0 && !fileName.includes('_chat') && isWhatsAppFormat) {
            console.log('Detected WhatsApp chat format, renaming file to _chat.txt');
            // Create a new file with the correct name for better backend processing
            file = new File([text], '_chat.txt', { type: 'text/plain' });
          }

          // For files opened from external sources, always use the text content directly
          // This ensures we're using the properly decoded content
          let result;

          if (text.length > 0) {
            // Always use _chat.txt as the filename for WhatsApp format
            const textFileName = '_chat.txt'; // Force WhatsApp format for better backend processing

            // Create a text file with the content
            const textFile = new File([text], textFileName, { type: 'text/plain' });
            console.log('Created text file for analysis:', textFile.name, 'size:', textFile.size);

            // Log a sample of the content for debugging
            console.log('Text content sample (first 200 chars):', text.substring(0, 200));

            // Check if there's a stored analysis type in localStorage
            const storedAnalysisType = localStorage.getItem('selectedAnalysisType');

            // If there's a stored analysis type, use it
            // Otherwise, determine the analysis type based on the chat content
            let analysisType: 'group' | 'love';

            if (storedAnalysisType && (storedAnalysisType === 'group' || storedAnalysisType === 'love')) {
              analysisType = storedAnalysisType as 'group' | 'love';
              console.log(`FileHandler: Using stored analysis type from localStorage (user's choice): ${analysisType}`);
            } else {
              // Only auto-detect if user hasn't made a choice on homepage
              console.log('FileHandler: No stored analysis type from user choice, detecting chat type before sending to backend...');
              const isGroupChatResult = isGroupChat(text);
              analysisType = isGroupChatResult ? 'group' : 'love';
              console.log(`FileHandler: Auto-detected chat type: ${isGroupChatResult ? 'group' : 'one-on-one'}, using analysis type: ${analysisType}`);
            }

            console.log(`FileHandler: IMPORTANT - Sending analysis type '${analysisType}' to backend API`);

            // **CONSUME SHARED FILE IMMEDIATELY** - Clear processing flag BEFORE API call
            // This ensures the shared file is "consumed" and won't be re-processed
            isProcessingSharedFileRef.current = false;
            console.log('Cleared processing flag BEFORE API call - shared file consumed');

            // Send the file to the backend with the appropriate analysis type
            console.log(`Sending file directly to backend for analysis with type: ${analysisType}`);
            result = await analyzeChatFile(textFile, analysisType);

            // If that fails, try sending the text directly
            if (result.source === 'mock') {
              console.log(`Fallback: Sending text directly to backend for analysis with type: ${analysisType}`);
              result = await analyzeChatText(text, textFileName, analysisType);
            }
          } else {
            // If we don't have text content, try sending the file directly
            // Default to group analysis for safety when we can't detect the chat type
            console.log('No text content available, sending file directly to backend for analysis:', file.name);
            console.log('Using default analysis type: group (since we cannot detect chat type without content)');
            const analysisType: 'group' | 'love' = 'group';
            
            // **CONSUME SHARED FILE IMMEDIATELY** - Clear processing flag BEFORE API call
            // This ensures the shared file is "consumed" and won't be re-processed
            isProcessingSharedFileRef.current = false;
            console.log('Cleared processing flag BEFORE API call (file direct) - shared file consumed');
            
            result = await analyzeChatFile(file, analysisType);
          }

          // Log the result for debugging
          console.log('Backend analysis result:', {
            source: result.source,
            hasResult: !!result.result,
            resultKeys: result.result ? Object.keys(result.result) : []
          });

          console.log('Analysis result source:', result.source);

          // Validate analysis result before proceeding
          if (!result.result) {
            console.error('⚡️  [log] - Analysis result is missing or empty:', result);
            throw new Error('Analysis completed but no result data received');
          }
          
          console.log('⚡️  [log] - Analysis result validation passed');
          
          // Store the analysis result in the context
          // We need to ensure analysisType is correctly set for both paths
          let finalAnalysisType: 'group' | 'love' = 'group';

          // Get from result if available, or from localStorage, or default to group
          if (result.analysisType && (result.analysisType === 'group' || result.analysisType === 'love')) {
            finalAnalysisType = result.analysisType;
            console.log(`Using analysis type from API result: ${finalAnalysisType}`);
          } else {
            // Try to get from localStorage
            const storedType = localStorage.getItem('selectedAnalysisType');
            if (storedType && (storedType === 'group' || storedType === 'love')) {
              finalAnalysisType = storedType as 'group' | 'love';
              console.log(`Using analysis type from localStorage: ${finalAnalysisType}`);
            } else {
              // Default to group if nothing else is available
              finalAnalysisType = 'group';
              console.log(`No analysis type found, defaulting to: ${finalAnalysisType}`);
            }
          }

          const newAnalysisData = {
            source: result.source as 'api' | 'file' | 'mock',
            fileName: result.fileName,
            text: result.text,
            result: result.result,
            analysisType: finalAnalysisType,
            selectedChatType: (finalAnalysisType === 'love' ? 'one-on-one' : 'group') as 'group' | 'one-on-one'
          };
          
          setAnalysisData(newAnalysisData);
          
          console.log('⚡️  [log] - Analysis data set, saving to history immediately');
          
          // Save analysis to history immediately with the new data
          console.log('⚡️  [log] - Attempting to save analysis to history...');
          const saveResult = await saveAnalysisToHistory(result.text || text, result.fileName, newAnalysisData);
          
          if (!saveResult?.success) {
            console.warn('⚡️  [log] - Initial save failed, attempting retry...');
            // Retry once after a brief delay
            await new Promise(resolve => setTimeout(resolve, 500));
            const retryResult = await saveAnalysisToHistory(result.text || text, result.fileName, newAnalysisData);
            
            if (!retryResult?.success) {
              console.error('⚡️  [log] - Retry save also failed');
              toast.error('Unable to save analysis to history after retry. Analysis is still available.');
            } else {
              console.log('⚡️  [log] - Retry save succeeded');
            }
          } else {
            console.log('⚡️  [log] - Initial save succeeded');
          }

          // Determine if this is a group chat or a 1-on-1 chat using the enhanced detector
          const chatContent = result.text || text;

          // Log a sample of the chat content for debugging
          console.log('FileHandler: Chat content sample (first 200 chars):',
            chatContent.substring(0, 200).replace(/\n/g, '\\n'));

          // Analyze the chat format
          analyzeWhatsAppChatFormat(chatContent);

          // Check if user made a choice on homepage first
          const storedAnalysisType = localStorage.getItem('selectedAnalysisType');
          let shouldNavigateToOneOnOne = false;

          if (storedAnalysisType && (storedAnalysisType === 'group' || storedAnalysisType === 'love')) {
            // Respect user's choice from homepage
            shouldNavigateToOneOnOne = storedAnalysisType === 'love';
            console.log(`FileHandler: Using stored user choice: ${storedAnalysisType}, navigating to ${shouldNavigateToOneOnOne ? '1-on-1' : 'group'}`);
          } else {
            // Only auto-detect if user hasn't made a choice
            console.log('FileHandler: No stored user choice, running enhanced group chat detection...');
            const isGroupChatResult = isGroupChat(chatContent);
            shouldNavigateToOneOnOne = !isGroupChatResult;
            console.log(`FileHandler: Auto-detected: ${isGroupChatResult ? 'group' : '1-on-1'}`);
          }

          // For debugging, also get the participant count using the old method
          const participantSet = new Set<string>();
          const chatLines = chatContent.split('\n');
          const messageLines = chatLines.filter((line: string) => line.includes(':'));
          messageLines.forEach((line: string) => {
            const match = line.match(/^([^:]+):/);
            if (match && match[1]) {
              participantSet.add(match[1].trim());
            }
          });
          const participants = Array.from(participantSet);
          const participantCount = participants.length;
          console.log(`FileHandler: Detected ${participantCount} participants in the chat:`, participants);

          // Mark file as processed to prevent duplicate analysis
          setFileProcessed(true);

          console.log('⚡️  [log] - Analysis completed and saved, navigating immediately');
          
          // Navigate based on the determined chat type
          if (shouldNavigateToOneOnOne) {
            // For 1-on-1 chats, navigate to the relationship type questionnaire
            console.log('FileHandler: Navigating to relationship type questionnaire for 1-on-1 chat');
            navigate('/relationship-type', { replace: true });
          } else {
            // For group chats, go to the dashboard
            console.log('FileHandler: Setting state to DASHBOARD for group chat');
            setState(AnalysisState.DASHBOARD);
            // Navigate to the home page if not already there
            navigate('/', { replace: true });
          }
        } catch (error) {
          console.error('Error analyzing file:', error);

          if (error.name === 'SubscriptionRequiredError') {
            // Keep loading screen visible during subscription flow
            // Clear processing flag on subscription error
            isProcessingSharedFileRef.current = false;
            console.log('Cleared processing flag due to subscription error');

            // Check if it's specifically a daily limit error
            if (isDailyLimitError(error)) {
              console.log('Daily limit error detected, showing daily limit dialog');
              showDailyLimitDialog();
              // Daily limit dialog will handle state reset
            } else {
              console.log('General subscription error, showing subscription modal');
              showSubscriptionModal(async () => {
                try {
                  // Retry the analysis after successful subscription
                  console.log('FileHandler: Retrying analysis after subscription success');
                  await performAnalysis();
                } catch (retryError) {
                  console.error('FileHandler: Analysis retry failed after subscription:', retryError);
                  setState(AnalysisState.UPLOAD);
                  toast.error('Analysis failed after subscription. Please try again.');
                }
              });
              // Subscription modal will handle state reset if user cancels
            }
            return;
          }

          // Clear processing flag on error
          isProcessingSharedFileRef.current = false;
          console.log('Cleared processing flag due to analysis error');

          // Show error dialog for API errors, no toast
          if (error.name === 'ApiError') {
            setErrorDialog({
              isOpen: true,
              title: 'Analysis Error',
              message: error.message
            });
          } else {
            // For other unexpected errors, show dialog with generic message
            setErrorDialog({
              isOpen: true,
              title: 'Something went wrong',
              message: 'An unexpected error occurred. Please try again.'
            });
          }

          // Return to main page after error - don't go to dashboard with mock data
          console.log('FileHandler: Error occurred, will return to main page after user dismisses dialog');
          setState(AnalysisState.UPLOAD);
        }
      };

      // Immediately show analysis screen and start analysis
      setState(AnalysisState.ANALYZING);

      // Let the API handle subscription validation - no preventive check
      await performAnalysis();
    };

    // Initialize file handling
    initFileHandling(handleProcessedFile, isProcessingSharedFileRef, isLoadingFromHistory);

    // Check if the app was opened with a file
    checkInitialFile(handleProcessedFile, isProcessingSharedFileRef, isLoadingFromHistory);
    
    // Cleanup function to clear processing flag when component unmounts
    return () => {
      if (isProcessingSharedFileRef.current) {
        console.log('Clearing processing flag on component unmount');
        isProcessingSharedFileRef.current = false;
      }
    };
  }, [navigate, setState, setAnalysisData]);

  // Clear processing flag when state changes to UPLOAD (user navigates back to upload)
  useEffect(() => {
    if (state === AnalysisState.UPLOAD && isProcessingSharedFileRef.current) {
      console.log('Clearing processing flag due to navigation to UPLOAD state');
      isProcessingSharedFileRef.current = false;
    }
  }, [state]);

  return (
    <>
      <AlertDialog open={errorDialog.isOpen} onOpenChange={(open) => 
        setErrorDialog(prev => ({ ...prev, isOpen: open }))
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{errorDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {errorDialog.message}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => {
              hapticFeedback.buttonPress();
              setErrorDialog(prev => ({ ...prev, isOpen: false }));
              // Navigate back to main page after error
              navigate('/', { replace: true });
            }}>
              Okay
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <DailyLimitReachedDialog
        open={isDailyLimitDialogOpen}
        onClose={() => {
          hapticFeedback.buttonPress();
          hideDailyLimitDialog();
        }}
      />
    </>
  );
};

export default FileHandler;
