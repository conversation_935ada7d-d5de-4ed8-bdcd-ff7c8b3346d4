/**
 * API configuration
 */

// Backend API URL configuration
export const API_CONFIG = {
  PRODUCTION_URL: 'https://chatbuster-backend.vibalyze.workers.dev',
  LOCALHOST_URL: 'http://localhost:8787',
  USE_LOCALHOST: true
};

// Helper function to check if the device is mobile
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Determine which API URL to use
export const getApiUrl = () => {
  // Force localhost when USE_LOCALHOST is true (for development)
  if (API_CONFIG.USE_LOCALHOST) {
    console.log('🔧 Using localhost backend:', API_CONFIG.LOCALHOST_URL);
    return API_CONFIG.LOCALHOST_URL;
  }

  return isMobileDevice() || import.meta.env.MODE === 'production'
    ? API_CONFIG.PRODUCTION_URL
    : API_CONFIG.LOCALHOST_URL;
};