
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/utils/toast';
import { isProductionMode } from '@/utils/environment';
import JSZip from 'jszip';
import { Capacitor } from '@capacitor/core';

const ChatUpload = ({ onAnalyze }: { onAnalyze: (text: string) => void }) => {
  const navigate = useNavigate();
  const [chatText, setChatText] = useState('');
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];

      // Check if it's a zip file
      if (file.name.toLowerCase().endsWith('.zip')) {
        // Processing indicated by UI loading state

        // Process zip file on the client side
        const reader = new FileReader();

        reader.onload = async (event) => {
          try {
            const zipData = event.target?.result;
            if (!zipData) {
              throw new Error('Failed to read zip file');
            }

            // Load the zip file
            const zip = await JSZip.loadAsync(zipData);

            // Find the WhatsApp chat export file (typically named with "_chat.txt")
            let chatFile = null;
            let chatFileName = '';

            // First, try to find a file with "_chat.txt" in the name (WhatsApp's standard format)
            for (const [path, zipEntry] of Object.entries(zip.files)) {
              if (path.toLowerCase().includes('_chat.txt') && !zipEntry.dir) {
                chatFile = zipEntry;
                chatFileName = path;
                break;
              }
            }

            // If no "_chat.txt" file found, look for any .txt file as fallback
            if (!chatFile) {
              for (const [path, zipEntry] of Object.entries(zip.files)) {
                if (path.toLowerCase().endsWith('.txt') && !zipEntry.dir) {
                  chatFile = zipEntry;
                  chatFileName = path;
                  break;
                }
              }
            }

            if (!chatFile) {
              throw new Error('No WhatsApp chat export (.txt file) found in the zip archive');
            }

            // Extract the text content
            const chatText = await chatFile.async('string');
            setChatText(chatText);
            // Success indicated by UI state change

          } catch (error) {
            console.error('Error processing zip file:', error);
            // Error handling should be done via dialogs
          }
        };

        reader.onerror = () => {
          // Error handling should be done via dialogs
        };

        // Read the zip file as an ArrayBuffer
        reader.readAsArrayBuffer(file);
      } else if (file.name.toLowerCase().endsWith('.json')) {
        // JSON file processing
        const reader = new FileReader();

        reader.onload = (event) => {
          const text = event.target?.result as string;
          try {
            // Validate JSON format
            JSON.parse(text);
            setChatText(text);
            // Success indicated by UI state change
          } catch (parseError) {
            console.error('Invalid JSON file:', parseError);
            // Error handling should be done via dialogs
          }
        };

        reader.onerror = () => {
          // Error handling should be done via dialogs
        };

        reader.readAsText(file);
      } else {
        // Regular text file processing
        const reader = new FileReader();

        reader.onload = (event) => {
          const text = event.target?.result as string;
          setChatText(text);
          // Success indicated by UI state change
        };

        reader.onerror = () => {
          // Error handling should be done via dialogs
        };

        reader.readAsText(file);
      }
    }
  };

  const handleAnalyzeClick = () => {
    // In production mode, redirect to tutorial instead of analyzing
    if (isProductionMode()) {
      console.log('Production mode - redirecting to tutorial');
      navigate('/tutorial', { replace: true });
      return;
    }

    if (chatText.trim().length < 10) {
      // User can see this requirement in UI
      return;
    }

    onAnalyze(chatText);
  };

  return (
    <div className="astro-card">
      <h2 className="text-xl font-semibold mb-4 text-center">Drop your chat. Let's vibe check it.</h2>

      <div
        className={`border-2 border-dashed rounded-xl p-6 mb-4 transition-all ${
          isDragging ? 'border-astro-lavender bg-astro-lavender/20' : 'border-gray-200'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Textarea
          placeholder="Paste your chat messages here or drag & drop a text/zip file..."
          className="min-h-[200px] bg-transparent border-none focus:ring-0 placeholder:text-gray-400"
          value={chatText}
          onChange={(e) => setChatText(e.target.value)}
        />
      </div>

      <div className="flex flex-col gap-3">
        <div className="flex flex-row gap-2 justify-center">
          <Button
            className="astro-button flex-1"
            onClick={handleAnalyzeClick}
          >
            Analyze My Group Chat
          </Button>

          {/* Only show the file picker button on mobile devices */}
          {Capacitor.isNativePlatform() && (
            <Button
              className="astro-button flex-none"
              onClick={() => {
                // Use the global file input handler
                if (typeof (window as any).openFileInput === 'function') {
                  (window as any).openFileInput();
                } else {
                  toast.error('File picker not available');
                }
              }}
            >
              📁 Select File
            </Button>
          )}
        </div>

        <div className="text-xs text-center text-gray-500 mt-2">
          Import from: WhatsApp (.txt, .zip, or .json) • Discord • Messenger • Texts
        </div>
      </div>
    </div>
  );
};

export default ChatUpload;
