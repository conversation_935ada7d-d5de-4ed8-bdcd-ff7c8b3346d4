import { getDeviceId } from './device';
import { DAILY_CREDITS_CONFIG } from '../config/credits';
import { getApiUrl } from '../config/api';
import { isDevelopmentMode } from '../utils/environment';

/**
 * Quota management service for tracking usage limits
 */

export interface QuotaStatus {
  allowed: boolean;
  remaining: number;
  tier: 'free' | 'premium';
  resetDate: string;
  message?: string;
}

export interface QuotaInfo {
  tier: 'free' | 'premium';
  remaining: number;
  resetDate: string;
  monthlyLimit: number;
  used: number;
}

// Get API URL from centralized config
const API_URL = getApiUrl();

/**
 * Get current quota status from backend
 */
export const getQuotaStatus = async (): Promise<QuotaStatus | null> => {
  try {
    const deviceId = await getDeviceId();
    const response = await fetch(`${API_URL}/quota`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${deviceId}`,
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Failed to get quota status:', response.status, response.statusText, errorText);
      // Throw an error to be caught by the calling function, which will show a toast
      throw new Error(`Failed to get quota status: ${response.status} ${errorText}`);
    }

    const quota = await response.json() as QuotaStatus;
    console.log('Quota status:', quota);
    return quota;
  } catch (error) {
    console.error('Error getting quota status:', error);
    // Re-throw the error so it can be handled by the UI
    throw new Error('Unable to check quota status. Please try again.');
  }
};

/**
 * Get detailed quota information for display
 */
export const getQuotaInfo = async (): Promise<QuotaInfo | null> => {
  const status = await getQuotaStatus();
  if (!status) return null;

  const limits = {
    free: 1,     // 1 total
    premium: DAILY_CREDITS_CONFIG.PREMIUM_DAILY_CREDITS  // daily credits
  };

  const limit = limits[status.tier];
  const used = limit - status.remaining;

  return {
    tier: status.tier,
    remaining: status.remaining,
    resetDate: status.resetDate,
    monthlyLimit: limit, // Keep same field name for compatibility
    used
  };
};

/**
 * Check if user can perform an analysis
 */
export const canAnalyze = async (): Promise<{ allowed: boolean; message?: string; quota?: QuotaStatus }> => {
  // Debug environment detection
  const devMode = isDevelopmentMode();
  console.log('🔍 Environment check:', {
    isDevelopmentMode: devMode,
    VITE_PRODUCTION_MODE: import.meta.env.VITE_PRODUCTION_MODE,
    NODE_ENV: import.meta.env.MODE,
    urlParams: window.location.search
  });

  // Bypass subscription check in development mode OR with URL parameter
  const forceBypass = window.location.search.includes('bypass=true');
  if (devMode || forceBypass) {
    console.log('🚀 Development mode/bypass: Bypassing subscription check for local testing');
    return {
      allowed: true,
      message: 'Development mode - unlimited access',
      quota: {
        allowed: true,
        remaining: 999,
        tier: 'premium',
        resetDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        message: 'Development mode'
      }
    };
  }

  const quota = await getQuotaStatus();

  if (!quota) {
    return {
      allowed: false,
      message: 'Unable to check quota status. Please try again.'
    };
  }

  if (!quota.allowed) {
    let message = quota.message;

    if (quota.tier === 'free' && quota.remaining === 0) {
      message = 'You\'ve used your free analysis. Upgrade to Premium for unlimited daily use!';
    } else if (quota.tier === 'premium' && quota.remaining === 0) {
      message = 'You\'re out of analyses for today. Come back tomorrow!';
    }

    return {
      allowed: false,
      message,
      quota
    };
  }

  return {
    allowed: true,
    quota
  };
};

/**
 * Format reset date for display
 */
export const formatResetDate = (resetDate: string): string => {
  try {
    // resetDate is in YYYY-MM format, so we need to add day and create next month
    const [year, month] = resetDate.split('-').map(Number);
    const nextMonth = month === 12 ? 1 : month + 1;
    const nextYear = month === 12 ? year + 1 : year;
    
    const resetDateTime = new Date(nextYear, nextMonth - 1, 1); // First day of next month
    
    return resetDateTime.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting reset date:', error);
    return 'Next month';
  }
};

/**
 * Get tier display information
 */
export const getTierInfo = (tier: 'free' | 'premium') => {
  const tierInfo = {
    free: {
      name: 'Free',
      monthlyLimit: 1,
      color: '#6B7280',
      description: '1 analysis total'
    },
    premium: {
      name: 'Premium',
      monthlyLimit: DAILY_CREDITS_CONFIG.PREMIUM_DAILY_CREDITS,
      color: '#F59E0B',
      description: 'Daily access'
    }
  };

  return tierInfo[tier];
};

/**
 * Calculate usage percentage
 */
export const getUsagePercentage = (used: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((used / total) * 100);
};